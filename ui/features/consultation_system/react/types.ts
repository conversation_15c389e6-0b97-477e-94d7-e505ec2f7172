// Consultation System Types

export interface FacultyTimeSlot {
  id: string
  start_time: string
  end_time: string
  day_of_week: string
  is_recurring: boolean
  specific_date?: string
  is_available: boolean
  notes?: string
  created_at: string
  updated_at: string
  pending_requests_count?: number
}

export interface TimeSlotFormData {
  start_time: string
  end_time: string
  day_of_week: string
  is_recurring: boolean
  specific_date: string
  is_available: boolean
  notes: string
}

export interface ConsultationRequest {
  id: string
  student_name: string
  student_id: string
  faculty_name: string
  preferred_datetime: string
  formatted_datetime: string
  description: string
  nature_of_concern: string
  concern_type_display: string
  status: 'pending' | 'approved' | 'declined' | 'completed' | 'cancelled'
  status_display: string
  faculty_comment?: string
  created_at: string
  updated_at: string
  can_be_approved: boolean
  can_be_declined: boolean
  can_be_completed: boolean
  college_campus_institute?: string
  department_program?: string
  semester?: string
  academic_year?: string
  place_of_consultation?: string
  intervention_given?: string
  referral_made?: string
  students_adviser_agreement?: boolean
  prepared_by_name?: string
  prepared_by_designation?: string
  noted_by_program_chair?: string
  noted_by_college_dean?: string
  conformance_signature?: string
  scf_number?: string
}

export interface ConsultationRequestFormData {
  faculty_time_slot_id: string
  preferred_datetime: string
  description: string
  nature_of_concern: string
  custom_concern?: string
  college_campus_institute?: string
  department_program?: string
  semester?: string
  academic_year?: string
  place_of_consultation?: string
  intervention_given?: string
  referral_made?: string
  students_adviser_agreement?: boolean
  prepared_by_name?: string
  prepared_by_designation?: string
  noted_by_program_chair?: string
  noted_by_college_dean?: string
  conformance_signature?: string
}

export interface ConsultationSummary {
  id: string
  student_name: string
  student_id: string
  consultation_date: string
  formatted_date: string
  concern_type: string
  concern_type_display: string
  description: string
  faculty_notes?: string
  outcome_summary?: string
  referral_made?: string
  follow_up_required?: string
  has_referral: boolean
  requires_follow_up: boolean
  duration_display: string
  created_at: string
  updated_at: string
}

export interface FacultyUser {
  id: string
  name: string
  department?: string
  available_slots_count: number
}

export interface StudentInfo {
  name: string
  student_id: string
  department?: string
  semester?: string
  academic_year?: string
  college_campus_institute?: string
  department_program?: string
  section?: string
}

export interface AvailableDateTime {
  datetime: string
  formatted_time: string
  slot_id: string
}

export interface TimeSlot {
  id: string
  datetime: string
  formatted_time: string
  is_available: boolean
  is_booked: boolean
  faculty_id: string
  created_at: string
  updated_at: string
}

export interface AvailableDate {
  date: string
  day_name: string
  slots_count: number
}

export interface ConsultationStatistics {
  total_consultations: number
  by_concern_type: Record<string, number>
  with_referrals: number
  requiring_follow_up: number
  average_per_month: number
}

export interface ApiResponse<T> {
  data?: T
  error?: string
  errors?: string[]
}

export interface TimeSlotResponse {
  time_slots: FacultyTimeSlot[]
  total_count: number
  available_count: number
}

export interface ConsultationRequestResponse {
  requests: ConsultationRequest[]
  total_count: number
}

export interface ConsultationSummaryResponse {
  summaries: ConsultationSummary[]
  total_count: number
}

export interface FacultyDashboardData {
  pending_requests: ConsultationRequest[]
  upcoming_consultations: ConsultationRequest[]
  statistics: ConsultationStatistics
}

export interface StudentFormData {
  student_info: StudentInfo
  available_faculty: FacultyUser[]
  concern_types: string[]
}

export interface ConsultationFilters {
  status?: string
  concern_type?: string
  student_search?: string
  content_search?: string
  start_date?: string
  end_date?: string
  page?: number
  per_page?: number
}

// Form validation types
export interface FormErrors {
  [key: string]: string
}

export interface FormState<T> {
  data: T
  errors: FormErrors
  loading: boolean
  submitted: boolean
}

// API Error types
export interface ApiError {
  message: string
  errors?: string[]
  status?: number
}

// Notification types
export interface NotificationMessage {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  message: string
  timeout?: number
}

// Calendar integration types
export interface CalendarEvent {
  id: string
  title: string
  start: string
  end: string
  description?: string
  location?: string
  attendees?: string[]
}

// Export/Import types
export interface ExportOptions {
  format: 'csv' | 'pdf' | 'excel'
  filters?: ConsultationFilters
  date_range?: {
    start: string
    end: string
  }
}

// Constants
export const CONCERN_TYPES = [
  'Personal',
  'Academic',
  'Teacher-related',
  'Co-students',
  'Family',
  'Others'
] as const

export const REQUEST_STATUSES = [
  'pending',
  'approved', 
  'declined',
  'completed',
  'cancelled'
] as const

export const DAYS_OF_WEEK = [
  'Monday',
  'Tuesday',
  'Wednesday', 
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday'
] as const

export type ConcernType = typeof CONCERN_TYPES[number]
export type RequestStatus = typeof REQUEST_STATUSES[number]
export type DayOfWeek = typeof DAYS_OF_WEEK[number]
