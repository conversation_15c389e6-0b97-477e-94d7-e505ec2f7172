import React, { useState, useEffect } from 'react'
import { Modal } from '@instructure/ui-modal'
import { View } from '@instructure/ui-view'
import { Flex } from '@instructure/ui-flex'
import { Heading } from '@instructure/ui-heading'
import { But<PERSON> } from '@instructure/ui-buttons'
import { IconButton } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import CanvasSelect from '@canvas/instui-bindings/react/Select'
import { IconArrowOpenStartLine, IconArrowOpenEndLine } from '@instructure/ui-icons'
import type { FacultyUser, AvailableDateTime } from '../types'
import styles from './FacultyCalendarModal.module.css'

interface TimeSlot {
  id: string
  datetime: string
  formatted_time: string
  is_available: boolean
  is_booked: boolean
  faculty_id: string
  created_at: string
  updated_at: string
}

interface FacultyCalendarModalProps {
  isOpen: boolean
  onClose: () => void
  faculty: FacultyUser | null
  onTimeSlotSelect: (datetime: string, slotId: string) => void
  selectedDateTime?: string
}

const FacultyCalendarModal: React.FC<FacultyCalendarModalProps> = ({
  isOpen,
  onClose,
  faculty,
  onTimeSlotSelect,
  selectedDateTime
}) => {
  const [currentWeek, setCurrentWeek] = useState(new Date())
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth())
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([])
  const [loading, setLoading] = useState(false)

  // Generate week dates
  const getWeekDates = (date: Date) => {
    const week = []
    const startOfWeek = new Date(date)
    const day = startOfWeek.getDay()
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1) // Monday as first day
    startOfWeek.setDate(diff)

    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek)
      day.setDate(startOfWeek.getDate() + i)
      week.push(day)
    }
    return week
  }

  const weekDates = getWeekDates(currentWeek)

  // Navigation functions
  const goToPreviousWeek = () => {
    const prevWeek = new Date(currentWeek)
    prevWeek.setDate(currentWeek.getDate() - 7)
    setCurrentWeek(prevWeek)
  }

  const goToNextWeek = () => {
    const nextWeek = new Date(currentWeek)
    nextWeek.setDate(currentWeek.getDate() + 7)
    setCurrentWeek(nextWeek)
  }

  const goToSelectedMonth = () => {
    const newDate = new Date(selectedYear, selectedMonth, 1)
    setCurrentWeek(newDate)
  }

  // Fetch time slots for the current week
  useEffect(() => {
    if (!faculty || !isOpen) return

    const fetchTimeSlots = async () => {
      setLoading(true)
      try {
        const startDate = weekDates[0].toISOString().split('T')[0]
        const endDate = weekDates[6].toISOString().split('T')[0]

        const response = await fetch(
          `/api/v1/consultation_requests/faculty/${faculty.id}/time_slots?start_date=${startDate}&end_date=${endDate}`
        )

        if (response.ok) {
          const data = await response.json()
          console.log('Fetched time slots:', data.time_slots)
          setTimeSlots(data.time_slots || [])
        } else {
          console.error('Failed to fetch time slots:', await response.text())

          // For testing/development - generate some sample slots if API fails
          const sampleSlots = generateSampleTimeSlots(faculty.id, weekDates)
          console.log('Using sample time slots:', sampleSlots)
          setTimeSlots(sampleSlots)
        }
      } catch (error) {
        console.error('Error fetching time slots:', error)

        // For testing/development - generate some sample slots if API fails
        const sampleSlots = generateSampleTimeSlots(faculty.id, weekDates)
        console.log('Using sample time slots due to error:', sampleSlots)
        setTimeSlots(sampleSlots)
      } finally {
        setLoading(false)
      }
    }

    fetchTimeSlots()
  }, [faculty, currentWeek, isOpen])

  // Helper function to generate sample time slots for testing/development
  const generateSampleTimeSlots = (facultyId: string, dates: Date[]) => {
    const slots: TimeSlot[] = []

    // Generate 3 sample slots per day
    dates.forEach(date => {
      // Skip weekends for sample data
      if (date.getDay() === 0 || date.getDay() === 6) return

      // Morning slot (9:00 AM)
      const morningSlot = new Date(date)
      morningSlot.setHours(9, 0, 0)

      // Afternoon slot (2:00 PM)
      const afternoonSlot = new Date(date)
      afternoonSlot.setHours(14, 0, 0)

      // Evening slot (4:30 PM)
      const eveningSlot = new Date(date)
      eveningSlot.setHours(16, 30, 0)

      // Add slots with different statuses
      slots.push({
        id: `sample-morning-${date.toISOString()}`,
        datetime: morningSlot.toISOString(),
        formatted_time: '9:00 AM',
        is_available: true,
        is_booked: false,
        faculty_id: facultyId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })

      slots.push({
        id: `sample-afternoon-${date.toISOString()}`,
        datetime: afternoonSlot.toISOString(),
        formatted_time: '2:00 PM',
        is_available: true,
        is_booked: date.getDate() % 2 === 0, // Every other day is booked
        faculty_id: facultyId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })

      slots.push({
        id: `sample-evening-${date.toISOString()}`,
        datetime: eveningSlot.toISOString(),
        formatted_time: '4:30 PM',
        is_available: date.getDate() % 3 !== 0, // Every third day is unavailable
        is_booked: false,
        faculty_id: facultyId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    })

    return slots
  }

  // Generate time slots for display (7 AM to 8 PM)
  const generateTimeSlots = () => {
    const slots = []
    for (let hour = 7; hour <= 20; hour++) {
      slots.push(`${hour.toString().padStart(2, '0')}:00`)
      if (hour < 20) {
        slots.push(`${hour.toString().padStart(2, '0')}:30`)
      }
    }
    return slots
  }

  const timeSlotHours = generateTimeSlots()

  // Get slot for specific date and time
  const getSlotForDateTime = (date: Date, time: string) => {
    const dateStr = date.toISOString().split('T')[0]
    const datetimeStr = `${dateStr}T${time}:00`
    return timeSlots.find(slot => slot.datetime.startsWith(datetimeStr))
  }

  // Handle time slot selection
  const handleSlotClick = (date: Date, time: string) => {
    const slot = getSlotForDateTime(date, time)
    if (slot && slot.is_available && !slot.is_booked) {
      onTimeSlotSelect(slot.datetime, slot.id)
      onClose()
    }
  }

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    })
  }

  // Generate month options
  const monthOptions = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]

  // Generate year options (current year ± 2)
  const currentYear = new Date().getFullYear()
  const yearOptions = []
  for (let year = currentYear - 1; year <= currentYear + 2; year++) {
    yearOptions.push(year)
  }

  return (
    <Modal
      open={isOpen}
      onDismiss={onClose}
      size="large"
      label="Faculty Consultation Calendar"
      shouldCloseOnDocumentClick={true}
    >
      <Modal.Header>
        <Heading level="h2">
          {faculty?.name} - Consultation Schedule
        </Heading>
      </Modal.Header>
      
      <Modal.Body>
        <View as="div" padding="medium">
          {/* Calendar Navigation */}
          <Flex justifyItems="space-between" alignItems="center" margin="0 0 medium 0">
            <Flex.Item>
              <Flex gap="small" alignItems="center">
                <IconButton
                  screenReaderLabel="Previous week"
                  onClick={goToPreviousWeek}
                  size="small"
                >
                  <IconArrowOpenStartLine />
                </IconButton>
                
                <Text weight="bold" size="large">
                  {weekDates[0].toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                </Text>
                
                <IconButton
                  screenReaderLabel="Next week"
                  onClick={goToNextWeek}
                  size="small"
                >
                  <IconArrowOpenEndLine />
                </IconButton>
              </Flex>
            </Flex.Item>
            
            <Flex.Item>
              <Flex gap="small" alignItems="center">
                <View as="div" margin="0 small 0 0">
                  <CanvasSelect
                    id="month-select"
                    label="Month"
                    value={selectedMonth.toString()}
                    onChange={(_e, value) => setSelectedMonth(parseInt(value))}
                  >
                    {monthOptions.map((month, index) => (
                      <option key={index} value={index.toString()}>
                        {month}
                      </option>
                    ))}
                  </CanvasSelect>
                </View>

                <View as="div" margin="0 small 0 0">
                  <CanvasSelect
                    id="year-select"
                    label="Year"
                    value={selectedYear.toString()}
                    onChange={(_e, value) => setSelectedYear(parseInt(value))}
                  >
                    {yearOptions.map(year => (
                      <option key={year} value={year.toString()}>
                        {year}
                      </option>
                    ))}
                  </CanvasSelect>
                </View>
                
                <Button onClick={goToSelectedMonth} size="small">
                  Go
                </Button>
              </Flex>
            </Flex.Item>
          </Flex>

          {/* Calendar Grid */}
          <View as="div" className={styles.calendarGrid}>
            {/* Week Header */}
            <Flex className={styles.weekHeader}>
              <Flex.Item size="80px" padding="small">
                <Text weight="bold" size="small">Time</Text>
              </Flex.Item>
              {weekDates.map((date, index) => (
                <Flex.Item key={index} shouldGrow padding="small" textAlign="center" className={styles.dayHeader}>
                  <Text weight="bold" size="small">
                    {formatDate(date)}
                  </Text>
                </Flex.Item>
              ))}
            </Flex>

            {/* Time Slots Grid */}
            {loading ? (
              <View as="div" padding="large" textAlign="center">
                <Text>Loading time slots...</Text>
              </View>
            ) : (
              <View as="div" maxHeight="400px" overflowY="auto">
                {timeSlotHours.map((time, timeIndex) => (
                  <View key={timeIndex} as="div" borderWidth="0 0 small 0">
                    <Flex>
                      <Flex.Item size="80px" padding="x-small" textAlign="center">
                        <Text size="small">{time}</Text>
                      </Flex.Item>
                    {weekDates.map((date, dateIndex) => {
                      const slot = getSlotForDateTime(date, time)
                      const isSelected = selectedDateTime && slot?.datetime === selectedDateTime
                      const isAvailable = slot?.is_available && !slot?.is_booked
                      const isBooked = slot?.is_booked
                      const isPast = new Date(`${date.toISOString().split('T')[0]}T${time}:00`) < new Date()
                      
                      return (
                        <Flex.Item 
                          key={dateIndex} 
                          shouldGrow 
                          padding="x-small"
                        >
                          <View
                            as="div"
                            height="30px"
                            borderRadius="small"
                            padding="xx-small"
                            cursor={isAvailable ? "pointer" : "default"}
                            background={
                              isSelected ? "brand" :
                              isBooked ? "danger" :
                              isAvailable ? "success" :
                              isPast ? "secondary" : "primary"
                            }
                            onClick={() => isAvailable && handleSlotClick(date, time)}
                            textAlign="center"
                          >
                            <Text 
                              size="x-small" 
                              color={
                                isSelected || isBooked || isAvailable ? "primary-inverse" : 
                                isPast ? "secondary" : "primary"
                              }
                            >
                              {isBooked ? "Booked" : 
                               isAvailable ? "Available" : 
                               isPast ? "Past" : ""}
                            </Text>
                          </View>
                        </Flex.Item>
                      )
                    })}
                    </Flex>
                  </View>
                ))}
              </View>
            )}
          </View>

          {/* Legend */}
          <Flex gap="medium" margin="medium 0 0 0" justifyItems="center">
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="success" borderRadius="small" />
                <Text size="small">Available</Text>
              </Flex>
            </Flex.Item>
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="danger" borderRadius="small" />
                <Text size="small">Booked</Text>
              </Flex>
            </Flex.Item>
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="brand" borderRadius="small" />
                <Text size="small">Selected</Text>
              </Flex>
            </Flex.Item>
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="secondary" borderRadius="small" />
                <Text size="small">Past</Text>
              </Flex>
            </Flex.Item>
          </Flex>
        </View>
      </Modal.Body>
      
      <Modal.Footer>
        <Button onClick={onClose}>Close</Button>
      </Modal.Footer>
    </Modal>
  )
}

export default FacultyCalendarModal
