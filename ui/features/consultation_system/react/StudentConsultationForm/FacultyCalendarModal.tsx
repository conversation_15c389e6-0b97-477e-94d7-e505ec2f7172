import React, { useState, useEffect } from 'react'
import { Modal } from '@instructure/ui-modal'
import { View } from '@instructure/ui-view'
import { Flex } from '@instructure/ui-flex'
import { Heading } from '@instructure/ui-heading'
import { But<PERSON> } from '@instructure/ui-buttons'
import { IconButton } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import { Select } from '@instructure/ui-select'
import { IconArrowOpenStartLine, IconArrowOpenEndLine } from '@instructure/ui-icons'
import type { FacultyUser, AvailableDateTime } from '../types'
import styles from './FacultyCalendarModal.module.css'

interface TimeSlot {
  id: string
  datetime: string
  formatted_time: string
  is_available: boolean
  is_booked: boolean
}

interface FacultyCalendarModalProps {
  isOpen: boolean
  onClose: () => void
  faculty: FacultyUser | null
  onTimeSlotSelect: (datetime: string, slotId: string) => void
  selectedDateTime?: string
}

const FacultyCalendarModal: React.FC<FacultyCalendarModalProps> = ({
  isOpen,
  onClose,
  faculty,
  onTimeSlotSelect,
  selectedDateTime
}) => {
  const [currentWeek, setCurrentWeek] = useState(new Date())
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth())
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([])
  const [loading, setLoading] = useState(false)

  // Generate week dates
  const getWeekDates = (date: Date) => {
    const week = []
    const startOfWeek = new Date(date)
    const day = startOfWeek.getDay()
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1) // Monday as first day
    startOfWeek.setDate(diff)

    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek)
      day.setDate(startOfWeek.getDate() + i)
      week.push(day)
    }
    return week
  }

  const weekDates = getWeekDates(currentWeek)

  // Navigation functions
  const goToPreviousWeek = () => {
    const prevWeek = new Date(currentWeek)
    prevWeek.setDate(currentWeek.getDate() - 7)
    setCurrentWeek(prevWeek)
  }

  const goToNextWeek = () => {
    const nextWeek = new Date(currentWeek)
    nextWeek.setDate(currentWeek.getDate() + 7)
    setCurrentWeek(nextWeek)
  }

  const goToSelectedMonth = () => {
    const newDate = new Date(selectedYear, selectedMonth, 1)
    setCurrentWeek(newDate)
  }

  // Fetch time slots for the current week
  useEffect(() => {
    if (!faculty || !isOpen) return

    const fetchTimeSlots = async () => {
      setLoading(true)
      try {
        const startDate = weekDates[0].toISOString().split('T')[0]
        const endDate = weekDates[6].toISOString().split('T')[0]
        
        const response = await fetch(
          `/api/v1/consultation_requests/faculty/${faculty.id}/time_slots?start_date=${startDate}&end_date=${endDate}`
        )
        
        if (response.ok) {
          const data = await response.json()
          setTimeSlots(data.time_slots || [])
        }
      } catch (error) {
        console.error('Error fetching time slots:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchTimeSlots()
  }, [faculty, currentWeek, isOpen])

  // Generate time slots for display (7 AM to 8 PM)
  const generateTimeSlots = () => {
    const slots = []
    for (let hour = 7; hour <= 20; hour++) {
      slots.push(`${hour.toString().padStart(2, '0')}:00`)
      if (hour < 20) {
        slots.push(`${hour.toString().padStart(2, '0')}:30`)
      }
    }
    return slots
  }

  const timeSlotHours = generateTimeSlots()

  // Get slot for specific date and time
  const getSlotForDateTime = (date: Date, time: string) => {
    const dateStr = date.toISOString().split('T')[0]
    const datetimeStr = `${dateStr}T${time}:00`
    return timeSlots.find(slot => slot.datetime.startsWith(datetimeStr))
  }

  // Handle time slot selection
  const handleSlotClick = (date: Date, time: string) => {
    const slot = getSlotForDateTime(date, time)
    if (slot && slot.is_available && !slot.is_booked) {
      onTimeSlotSelect(slot.datetime, slot.id)
      onClose()
    }
  }

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    })
  }

  // Generate month options
  const monthOptions = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]

  // Generate year options (current year ± 2)
  const currentYear = new Date().getFullYear()
  const yearOptions = []
  for (let year = currentYear - 1; year <= currentYear + 2; year++) {
    yearOptions.push(year)
  }

  return (
    <Modal
      open={isOpen}
      onDismiss={onClose}
      size="large"
      label="Faculty Consultation Calendar"
      shouldCloseOnDocumentClick={true}
    >
      <Modal.Header>
        <Heading level="h2">
          {faculty?.name} - Consultation Schedule
        </Heading>
      </Modal.Header>
      
      <Modal.Body>
        <View as="div" padding="medium">
          {/* Calendar Navigation */}
          <Flex justifyItems="space-between" alignItems="center" margin="0 0 medium 0">
            <Flex.Item>
              <Flex gap="small" alignItems="center">
                <IconButton
                  screenReaderLabel="Previous week"
                  onClick={goToPreviousWeek}
                  size="small"
                >
                  <IconArrowOpenStartLine />
                </IconButton>
                
                <Text weight="bold" size="large">
                  {weekDates[0].toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                </Text>
                
                <IconButton
                  screenReaderLabel="Next week"
                  onClick={goToNextWeek}
                  size="small"
                >
                  <IconArrowOpenEndLine />
                </IconButton>
              </Flex>
            </Flex.Item>
            
            <Flex.Item>
              <Flex gap="small" alignItems="center">
                <Select
                  renderLabel="Month"
                  value={selectedMonth.toString()}
                  onChange={(e, { value }) => setSelectedMonth(parseInt(value as string))}
                  width="120px"
                >
                  {monthOptions.map((month, index) => (
                    <Select.Option key={index} id={index.toString()} value={index.toString()}>
                      {month}
                    </Select.Option>
                  ))}
                </Select>
                
                <Select
                  renderLabel="Year"
                  value={selectedYear.toString()}
                  onChange={(e, { value }) => setSelectedYear(parseInt(value as string))}
                  width="80px"
                >
                  {yearOptions.map(year => (
                    <Select.Option key={year} id={year.toString()} value={year.toString()}>
                      {year}
                    </Select.Option>
                  ))}
                </Select>
                
                <Button onClick={goToSelectedMonth} size="small">
                  Go
                </Button>
              </Flex>
            </Flex.Item>
          </Flex>

          {/* Calendar Grid */}
          <View as="div" className={styles.calendarGrid}>
            {/* Week Header */}
            <Flex className={styles.weekHeader}>
              <Flex.Item size="80px" padding="small">
                <Text weight="bold" size="small">Time</Text>
              </Flex.Item>
              {weekDates.map((date, index) => (
                <Flex.Item key={index} shouldGrow padding="small" textAlign="center" className={styles.dayHeader}>
                  <Text weight="bold" size="small">
                    {formatDate(date)}
                  </Text>
                </Flex.Item>
              ))}
            </Flex>

            {/* Time Slots Grid */}
            {loading ? (
              <View as="div" padding="large" textAlign="center">
                <Text>Loading time slots...</Text>
              </View>
            ) : (
              <View as="div" maxHeight="400px" overflowY="auto">
                {timeSlotHours.map((time, timeIndex) => (
                  <Flex key={timeIndex} borderWidth="0 0 small 0">
                    <Flex.Item size="80px" padding="x-small" textAlign="center">
                      <Text size="small">{time}</Text>
                    </Flex.Item>
                    {weekDates.map((date, dateIndex) => {
                      const slot = getSlotForDateTime(date, time)
                      const isSelected = selectedDateTime && slot?.datetime === selectedDateTime
                      const isAvailable = slot?.is_available && !slot?.is_booked
                      const isBooked = slot?.is_booked
                      const isPast = new Date(`${date.toISOString().split('T')[0]}T${time}:00`) < new Date()
                      
                      return (
                        <Flex.Item 
                          key={dateIndex} 
                          shouldGrow 
                          padding="x-small"
                        >
                          <View
                            as="div"
                            height="30px"
                            borderRadius="small"
                            padding="xx-small"
                            cursor={isAvailable ? "pointer" : "default"}
                            background={
                              isSelected ? "brand" :
                              isBooked ? "danger" :
                              isAvailable ? "success" :
                              isPast ? "secondary" : "primary"
                            }
                            onClick={() => isAvailable && handleSlotClick(date, time)}
                            textAlign="center"
                          >
                            <Text 
                              size="x-small" 
                              color={
                                isSelected || isBooked || isAvailable ? "primary-inverse" : 
                                isPast ? "secondary" : "primary"
                              }
                            >
                              {isBooked ? "Booked" : 
                               isAvailable ? "Available" : 
                               isPast ? "Past" : ""}
                            </Text>
                          </View>
                        </Flex.Item>
                      )
                    })}
                  </Flex>
                ))}
              </View>
            )}
          </View>

          {/* Legend */}
          <Flex gap="medium" margin="medium 0 0 0" justifyItems="center">
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="success" borderRadius="small" />
                <Text size="small">Available</Text>
              </Flex>
            </Flex.Item>
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="danger" borderRadius="small" />
                <Text size="small">Booked</Text>
              </Flex>
            </Flex.Item>
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="brand" borderRadius="small" />
                <Text size="small">Selected</Text>
              </Flex>
            </Flex.Item>
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="secondary" borderRadius="small" />
                <Text size="small">Past</Text>
              </Flex>
            </Flex.Item>
          </Flex>
        </View>
      </Modal.Body>
      
      <Modal.Footer>
        <Button onClick={onClose}>Close</Button>
      </Modal.Footer>
    </Modal>
  )
}

export default FacultyCalendarModal
